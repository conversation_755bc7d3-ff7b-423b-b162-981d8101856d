<template>
  <div class="app-container home">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <h1>会议室预约管理系统</h1>
        <p>高效便捷的会议室预约平台，让会议安排更简单</p>
        <div class="quick-actions">
          <el-button type="primary" size="large" icon="Plus" @click="goToMyReservations">
            快速预约
          </el-button>
          <el-button type="success" size="large" icon="Search" @click="goToRoomList">
            查看会议室
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 我的预约 -->
      <!-- <el-col :xs="24" :sm="24" :md="12" :lg="8"> -->
      <el-col :xs="12" :sm="12" :md="12" :lg="12">
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <span><el-icon><User /></el-icon> 我的预约</span>
              <el-button type="text" @click="goToMyReservations">查看全部</el-button>
            </div>
          </template>
          <div class="my-reservations">
            <div v-if="myReservations.length === 0" class="empty-state">
              <el-icon><DocumentRemove /></el-icon>
              <p>暂无预约记录</p>
            </div>
            <div v-else>
              <div
                v-for="reservation in myReservations"
                :key="reservation.reservationId"
                class="reservation-item"
              >
                <div class="reservation-info">
                  <div class="meeting-title">{{ reservation.meetingTitle }}</div>
                  <div class="meeting-details">
                    <span class="room-name">{{ getRoomName(reservation.roomId) }}</span>
                    <span class="meeting-time">
                      {{ formatDateTime(reservation.startTime) }} - {{ formatDateTime(reservation.endTime) }}
                    </span>
                  </div>
                </div>
                <div class="reservation-status">
                  <el-tag :type="getStatusType(reservation.status)">
                    {{ getStatusText(reservation.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 热门会议室 -->
      <el-col :xs="12" :sm="12" :md="12" :lg="12">
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <span><el-icon><Star /></el-icon> 会议室列表</span>
              <el-button type="text" @click="goToRoomList">查看全部</el-button>
            </div>
          </template>
          <div class="popular-rooms">
            <div v-if="popularRooms.length === 0" class="empty-state">
              <el-icon><OfficeBuilding /></el-icon>
              <p>暂无会议室数据</p>
            </div>
            <div v-else>
              <div
                v-for="room in popularRooms"
                :key="room.roomId"
                class="room-item"
                @click="handleRoomReservation(room)"
              >
                <div class="room-image">
                  <img v-if="getImageUrl(room.imageUrl)" :src="getImageUrl(room.imageUrl)" :alt="room.roomName" />
                  <div v-else class="room-placeholder">
                    <el-icon><OfficeBuilding /></el-icon>
                  </div>
                </div>
                <div class="room-info">
                  <div class="room-name">{{ room.roomName }}</div>
                  <div class="room-capacity">
                    <el-icon><User /></el-icon>
                    容纳 {{ room.capacity }} 人 {{ room.remark }}
                  </div>
                </div>
                <div class="room-status">
                  <el-tag :type="getRoomStatusType(room.status)" size="small">
                    {{ getRoomStatusText(room.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时状态显示屏 -->
    <div class="status-display-section">
      <div class="status-header">
        <h2 class="status-title">
          <el-icon><Monitor /></el-icon>
          会议室实时状态
        </h2>
        <div class="status-time">{{ currentDateTime }}</div>
      </div>

      <el-row :gutter="16" class="status-grid">
        <el-col
          v-for="room in roomStatusList"
          :key="room.roomId"
          :xs="12"
          :sm="12"
          :md="12"
          :lg="12"
          class="status-card-col"
        >
          <el-card class="status-card" :class="getCardClass(room)">
            <div class="room-info">
              <div class="room-name">{{ room.roomName }}</div>
              <div class="room-capacity">容量: {{ room.capacity }}人</div>
              <el-tag
                :type="getStatusTagType(room.currentStatus)"
                class="status-tag"
                size="small"
              >
                {{ getMeetingStatusText(room.currentStatus) }}
              </el-tag>
            </div>

            <div class="meeting-info">
              <!-- 显示今天所有会议 -->
              <div v-if="room.todayMeetings && room.todayMeetings.length > 0" class="today-meetings">
                <div
                  v-for="(meeting, index) in room.todayMeetings"
                  :key="index"
                  class="meeting-item"
                  :class="getMeetingItemClass(meeting)"
                >
                  <div class="meeting-header">
                    <div class="meeting-title">会议主题: {{ meeting.meetingTitle }}</div>
                    <el-tag
                      :type="getMeetingStatusType(meeting.status)"
                      size="small"
                    >
                      {{ getMeetingStatus(meeting.status) }}
                    </el-tag>
                  </div>
                  <div class="meeting-time">
                    会议时间: {{ formatDateTime(meeting.startTime) }} - {{ formatDateTime(meeting.endTime) }}
                  </div>
                  <div class="meeting-organizer">预约人: {{ meeting.organizer }}</div>

                  <!-- 如果是正在进行的会议，显示进度 -->
                  <div v-if="meeting.status === 'ongoing'" class="meeting-progress">
                    <el-progress
                      :percentage="meeting.progressPercent"
                      :color="getProgressColor(meeting.progressPercent)"
                      :stroke-width="4"
                      :show-text="false"
                    />
                    <span class="progress-text">{{ meeting.progressPercent }}%</span>
                  </div>

                  <!-- 如果是即将开始的会议，显示倒计时 -->
                  <div v-else-if="meeting.status === 'upcoming'" class="countdown">
                    <el-icon><Clock /></el-icon>
                    {{ getCountdown(meeting.startTime) }}
                  </div>
                </div>
              </div>

              <!-- 空闲状态 -->
              <div v-else class="no-meeting">
                <el-icon><Check /></el-icon>
                <span>今日空闲</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 刷新按钮 -->
      <div class="status-actions">
        <el-button
          type="primary"
          :icon="Refresh"
          @click="refreshRoomStatus"
          :loading="statusLoading"
          size="large"
        >
          刷新状态
        </el-button>
        <span class="last-update">最后更新: {{ lastUpdateTime }}</span>
      </div>
    </div>
  </div>
</template>

<script setup name="Index">
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User,
  DocumentRemove,
  Star,
  OfficeBuilding,
  Monitor,
  Clock,
  Check,
  Refresh
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import {
  listReservation,
  myReservationList
} from '@/api/reserve/reservation'
import { listRoom } from '@/api/search/room'

const router = useRouter()

// 响应式数据
const stats = ref({
  totalRooms: 0,
  availableRooms: 0,
  pendingReservations: 0,
  todayReservations: 0
})

const myReservations = ref([])
const popularRooms = ref([])
const todaySchedule = ref([])
const availableRooms = ref([])
const selectedDate = ref(new Date())
const loading = ref(false)

// 实时状态显示相关数据
const roomStatusList = ref([])
const currentDateTime = ref('')
const lastUpdateTime = ref('')
const statusLoading = ref(false)
let statusTimer = null

// 计算属性
const roomMap = computed(() => {
  const map = {}
  popularRooms.value.forEach(room => {
    map[room.roomId] = room
  })
  return map
})

// 生命周期
onMounted(() => {
  loadDashboardData()
  initRoomStatus()
})

onUnmounted(() => {
  if (statusTimer) {
    clearInterval(statusTimer)
  }
})

// 方法
async function loadDashboardData() {
  loading.value = true
  try {
    await Promise.all([
      loadStats(),
      loadMyReservations(),
      loadPopularRooms(),
      loadTodaySchedule(),
      loadAvailableRooms()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

async function loadStats() {
  try {
    // 获取会议室统计
    const roomResponse = await listRoom({ pageNum: 1, pageSize: 1000 })
    const rooms = roomResponse.rows || []
    stats.value.totalRooms = rooms.length
    stats.value.availableRooms = rooms.filter(room => room.status === '1').length

    // 获取预约统计
    const reservationResponse = await listReservation({
      pageNum: 1,
      pageSize: 1000,
      status: '1' // 待审核
    })
    stats.value.pendingReservations = reservationResponse.total || 0

    // 获取今日预约统计
    const today = new Date()
    const todayStr = formatDate(today)
    const todayReservationResponse = await listReservation({
      pageNum: 1,
      pageSize: 1000,
      startTime: todayStr + ' 00:00:00',
      endTime: todayStr + ' 23:59:59'
    })
    stats.value.todayReservations = todayReservationResponse.total || 0
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

async function loadMyReservations() {
  try {
    const response = await myReservationList({
      pageNum: 1,
      pageSize: 5
    })
    myReservations.value = response.rows || []
  } catch (error) {
    console.error('加载我的预约失败:', error)
  }
}

function getImageUrl(path) {
  return `http://localhost:8080${path}`
}

async function loadPopularRooms() {
  try {
    const response = await listRoom({
      pageNum: 1,
      pageSize: 8,
      status: '1' // 只获取启用的会议室
    })
    console.log(response.rows)
    popularRooms.value = response.rows || []
  } catch (error) {
    console.error('加载热门会议室失败:', error)
  }
}

async function loadTodaySchedule() {
  try {
    const dateStr = formatDate(selectedDate.value)
    const response = await listReservation({
      pageNum: 1,
      pageSize: 20,
      startTime: dateStr + ' 00:00:00',
      endTime: dateStr + ' 23:59:59',
      status: '2' // 已通过的预约
    })
    todaySchedule.value = (response.rows || []).sort((a, b) =>
      new Date(a.startTime) - new Date(b.startTime)
    )
  } catch (error) {
    console.error('加载今日安排失败:', error)
  }
}

async function loadAvailableRooms() {
  try {
    const response = await listRoom({
      pageNum: 1,
      pageSize: 100,
      status: '1' // 只获取启用的会议室
    })
    availableRooms.value = response.rows || []
  } catch (error) {
    console.error('加载可用会议室失败:', error)
  }
}

function handleRoomReservation(room) {
  quickForm.roomId = room.roomId
  // handleQuickReservation()
}

// 导航函数
function goToMyReservations() {
  router.push('/reserve/myReservation')
}

function goToRoomList() {
  router.push('/search/room')
}

// 工具函数
function getRoomName(roomId) {
  const room = roomMap.value[roomId]
  return room ? room.roomName : `会议室${roomId}`
}

function getStatusType(status) {
  const statusMap = {
    '0': 'info',    // 已取消
    '1': 'warning', // 待审核
    '2': 'success', // 已通过
    '3': 'danger',  // 已拒绝
    '4': 'success'  // 已完成
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    '0': '已取消',
    '1': '待审核',
    '2': '已通过',
    '3': '已拒绝',
    '4': '已完成'
  }
  return statusMap[status] || '未知'
}

function getRoomStatusType(status) {
  const statusMap = {
    '0': 'danger',  // 停用
    '1': 'success', // 启用
    '2': 'warning'  // 维护中
  }
  return statusMap[status] || 'info'
}

function getRoomStatusText(status) {
  const statusMap = {
    '0': '停用',
    '1': '启用',
    '2': '维护中'
  }
  return statusMap[status] || '未知'
}

function formatDate(date) {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

function formatDateTime(dateTime) {
  if (!dateTime) return ''
  const d = new Date(dateTime)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 实时状态显示相关方法
function initRoomStatus() {
  updateCurrentDateTime()
  loadRoomStatus()

  // 设置定时器，每30秒更新一次状态
  statusTimer = setInterval(() => {
    updateCurrentDateTime()
    loadRoomStatus()
  }, 30000)

  // 每秒更新时间显示
  setInterval(updateCurrentDateTime, 1000)
}

function updateCurrentDateTime() {
  const now = new Date()
  const weekDays = ['日', '一', '二', '三', '四', '五', '六']
  currentDateTime.value = `${now.getFullYear()}年${(now.getMonth() + 1).toString().padStart(2, '0')}月${now.getDate().toString().padStart(2, '0')}日 星期${weekDays[now.getDay()]} ${now.toLocaleTimeString()}`
}

async function loadRoomStatus() {
  try {
    statusLoading.value = true

    // 获取会议室列表
    const roomResponse = await listRoom({ status: '1' })
    const rooms = roomResponse.rows || []

    // 获取今天的预约列表（已通过的）
    const today = new Date()
    const todayStr = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`

    const reservationResponse = await listReservation({
      status: 2, // 已通过
      startTime: todayStr
    })
    const reservations = reservationResponse.rows || []

    // 组装会议室状态数据
    const roomsWithMeetings = rooms.map(room => {
      const roomReservations = reservations.filter(r => r.roomId === room.roomId)
      const now = new Date()

      // 处理今天实时会议，过滤已结束的会议，按时间排序
      const todayMeetings = roomReservations
        .map(meeting => {
          const startTime = new Date(meeting.startTime)
          const endTime = new Date(meeting.endTime)

          // 确定会议状态
          let status = 'upcoming' // 即将开始
          if (now >= endTime) {
            status = 'completed' // 已结束
          } else if (now >= startTime && now <= endTime) {
            status = 'ongoing' // 进行中
          }

          return {
            ...meeting,
            organizer: meeting.userName || meeting.applyUserName || meeting.createBy || '未知',
            status,
            progressPercent: status === 'ongoing' ? calculateMeetingProgress(meeting.startTime, meeting.endTime) : 0
          }
        })
        .filter(meeting => meeting.status !== 'completed') // 过滤掉已结束的会议
        .sort((a, b) => new Date(a.startTime) - new Date(b.startTime))

      // 确定会议室当前状态
      let currentStatus = 'available' // 空闲
      const ongoingMeeting = todayMeetings.find(m => m.status === 'ongoing')
      const upcomingMeeting = todayMeetings.find(m => m.status === 'upcoming')

      if (ongoingMeeting) {
        currentStatus = 'occupied' // 使用中
      } else if (upcomingMeeting) {
        const nextStartTime = new Date(upcomingMeeting.startTime)
        const timeDiff = nextStartTime - now
        if (timeDiff <= 15 * 60 * 1000) { // 15分钟内有会议
          currentStatus = 'preparing' // 准备中
        }
      }

      return {
        ...room,
        currentStatus,
        todayMeetings,
        meetingCount: todayMeetings.length // 添加会议数量字段
      }
    })

    // 按会议数量排序：会议多的排在前面，没有会议的排在后面
    roomStatusList.value = roomsWithMeetings.sort((a, b) => {
      // 首先按会议数量降序排列（会议多的在前）
      if (b.meetingCount !== a.meetingCount) {
        return b.meetingCount - a.meetingCount
      }

      // 如果会议数量相同，按会议室名称升序排列
      return a.roomName.localeCompare(b.roomName)
    })

    lastUpdateTime.value = new Date().toLocaleTimeString()
  } catch (error) {
    console.error('加载会议室状态失败:', error)
    ElMessage.error('加载会议室状态失败')
  } finally {
    statusLoading.value = false
  }
}

function calculateMeetingProgress(startTime, endTime) {
  const now = new Date()
  const start = new Date(startTime)
  const end = new Date(endTime)

  if (now <= start) return 0
  if (now >= end) return 100

  const total = end - start
  const elapsed = now - start
  return Math.round((elapsed / total) * 100)
}

function getStatusTagType(status) {
  const typeMap = {
    'available': 'success',
    'occupied': 'danger',
    'preparing': 'warning',
    'maintenance': 'info'
  }
  return typeMap[status] || 'info'
}

function getMeetingStatusText(status) {
  const textMap = {
    'available': '空闲',
    'occupied': '使用中',
    'preparing': '准备中',
    'maintenance': '维护中'
  }
  return textMap[status] || '未知'
}

function getCardClass(room) {
  return {
    'status-available': room.currentStatus === 'available',
    'status-occupied': room.currentStatus === 'occupied',
    'status-preparing': room.currentStatus === 'preparing',
    'status-maintenance': room.currentStatus === 'maintenance'
  }
}

// function formatTime(dateTime) {
//   if (!dateTime) return ''
//   const date = new Date(dateTime)
//   return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
// }

function getCountdown(startTime) {
  const now = new Date()
  const start = new Date(startTime)
  const diff = start - now

  if (diff <= 0) return '即将开始'

  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (hours > 0) {
    return `${hours}小时${minutes}分钟后开始`
  } else {
    return `${minutes}分钟后开始`
  }
}

function getProgressColor(percentage) {
  if (percentage < 30) return '#67c23a'
  if (percentage < 70) return '#e6a23c'
  return '#f56c6c'
}

function refreshRoomStatus() {
  loadRoomStatus()
}

// 新增的会议状态相关方法
function getMeetingItemClass(meeting) {
  return {
    'meeting-ongoing': meeting.status === 'ongoing',
    'meeting-upcoming': meeting.status === 'upcoming'
  }
}

function getMeetingStatusType(status) {
  const typeMap = {
    'ongoing': 'danger',
    'upcoming': 'warning'
  }
  return typeMap[status] || 'info'
}

function getMeetingStatus(status) {
  const textMap = {
    'ongoing': '进行中',
    'upcoming': '即将开始'
  }
  return textMap[status] || '未知'
}
</script>

<style scoped lang="scss">
.home {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

// 欢迎横幅
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 40px;
  margin-bottom: 24px;
  color: white;
  text-align: center;

  .banner-content {
    h1 {
      font-size: 32px;
      font-weight: 600;
      margin: 0 0 12px 0;
    }

    p {
      font-size: 16px;
      margin: 0 0 32px 0;
      opacity: 0.9;
    }

    .quick-actions {
      .el-button {
        margin: 0 8px;
        padding: 12px 24px;
        font-size: 16px;
      }
    }
  }
}

// 主要功能区域
.main-content {
  margin-bottom: 24px;
}

.feature-card {
  height: 100%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      display: flex;
      align-items: center;
      font-weight: 600;

      .el-icon {
        margin-right: 8px;
      }
    }
  }
}

// 我的预约
.my-reservations {
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #909399;

    .el-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 0 0 16px 0;
    }
  }

  .reservation-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .reservation-info {
      flex: 1;

      .meeting-title {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .meeting-details {
        font-size: 12px;
        color: #909399;

        .room-name {
          margin-right: 12px;
        }
      }
    }

    .reservation-status {
      flex-shrink: 0;
    }
  }
}

// 热门会议室
.popular-rooms {
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #909399;

    .el-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 0;
    }
  }

  .room-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }

    .room-image {
      width: 48px;
      height: 48px;
      margin-right: 12px;
      border-radius: 4px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .room-placeholder {
        width: 100%;
        height: 100%;
        background-color: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #c0c4cc;
      }
    }

    .room-info {
      flex: 1;

      .room-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .room-capacity {
        font-size: 12px;
        color: #909399;
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
}

// 实时状态显示屏样式
.status-display-section {
  margin-top: 40px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;

  .status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .status-title {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: 600;
      margin: 0;

      .el-icon {
        margin-right: 12px;
        font-size: 28px;
      }
    }

    .status-time {
      font-size: 16px;
      opacity: 0.9;
      font-weight: 500;
    }
  }

  .status-grid {
    margin-bottom: 24px;
  }

  .status-card-col {
    margin-bottom: 16px;
  }

  .status-card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }

    &.status-available {
      border-left: 4px solid #67c23a;
    }

    &.status-occupied {
      border-left: 4px solid #f56c6c;
    }

    &.status-preparing {
      border-left: 4px solid #e6a23c;
    }

    &.status-maintenance {
      border-left: 4px solid #909399;
    }
  }

  .room-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .room-name {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .room-capacity {
      font-size: 14px;
      color: #606266;
      margin-top: 4px;
    }

    .status-tag {
      font-weight: 500;
    }
  }

  .meeting-info {
    .today-meetings {
      .meeting-item {
        padding: 12px;
        margin-bottom: 12px;
        border-radius: 8px;
        border: 1px solid #e4e7ed;
        transition: all 0.3s ease;

        &:last-child {
          margin-bottom: 0;
        }

        &.meeting-ongoing {
          border-color: #f56c6c;
          background-color: #fef0f0;
        }

        &.meeting-upcoming {
          border-color: #e6a23c;
          background-color: #fdf6ec;
        }

        .meeting-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .meeting-title {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
            flex: 1;
            margin-right: 8px;
          }
        }

        .meeting-time {
          font-size: 14px;
          color: #606266;
          margin-bottom: 8px;
        }

        .meeting-organizer {
          font-size: 14px;
          color: #909399;
          margin-bottom: 8px;
        }

        .meeting-progress {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .el-progress {
            flex: 1;
            margin-right: 12px;
          }

          .progress-text {
            font-size: 14px;
            font-weight: 500;
            color: #606266;
          }
        }

        .countdown {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #e6a23c;
          font-weight: 500;

          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }

    .no-meeting {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      color: #67c23a;
      font-size: 16px;
      font-weight: 500;

      .el-icon {
        margin-right: 8px;
        font-size: 20px;
      }
    }
  }

  .status-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;

    .last-update {
      font-size: 14px;
      opacity: 0.8;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .welcome-banner {
    padding: 24px;

    .banner-content {
      h1 {
        font-size: 24px;
      }

      p {
        font-size: 14px;
      }

      .quick-actions {
        .el-button {
          padding: 8px 16px;
          font-size: 14px;
        }
      }
    }
  }

  .stat-card {
    padding: 16px;

    .stat-icon {
      width: 40px;
      height: 40px;
      font-size: 20px;
      margin-right: 12px;
    }

    .stat-content {
      .stat-number {
        font-size: 20px;
      }
    }
  }

  .operation-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .operation-item {
    padding: 16px;

    .operation-icon {
      width: 40px;
      height: 40px;
      font-size: 18px;
      margin-bottom: 8px;
    }

    .operation-text {
      font-size: 12px;
    }
  }

  .timeline-item {
    .timeline-time {
      width: 80px;
      margin-right: 12px;

      .time-start {
        font-size: 14px;
      }
    }
  }

  // 实时状态显示屏移动端样式
  .status-display-section {
    margin-top: 24px;
    padding: 16px;

    .status-header {
      flex-direction: column;
      align-items: center;
      text-align: center;
      margin-bottom: 20px;

      .status-title {
        font-size: 20px;
        margin-bottom: 8px;

        .el-icon {
          font-size: 24px;
        }
      }

      .status-time {
        font-size: 14px;
      }
    }

    .status-card {
      .room-info {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 12px;

        .room-name {
          font-size: 16px;
          margin-bottom: 4px;
        }

        .room-capacity {
          margin-bottom: 8px;
        }

        .status-tag {
          align-self: flex-end;
        }
      }

      .meeting-info {
        .today-meetings {
          .meeting-item {
            padding: 8px;
            margin-bottom: 8px;

            .meeting-header {
              flex-direction: column;
              align-items: flex-start;

              .meeting-title {
                font-size: 14px;
                margin-bottom: 4px;
                margin-right: 0;
              }
            }

            .meeting-time {
              font-size: 12px;
            }

            .meeting-organizer {
              font-size: 12px;
            }

            .meeting-progress {
              .progress-text {
                font-size: 12px;
              }
            }

            .countdown {
              font-size: 12px;
            }
          }
        }

        .no-meeting {
          padding: 16px;
          font-size: 14px;

          .el-icon {
            font-size: 18px;
          }
        }
      }
    }

    .status-actions {
      flex-direction: column;
      gap: 12px;

      .last-update {
        font-size: 12px;
      }
    }
  }
}
</style>